#!/usr/bin/env python3
"""
HL7 Message Validator <PERSON> (Final Version)

This script validates HL7 messages from a given folder path including subfolders.
This version includes fixes for line-endings, validation rules, and correct
0-based indexing for the python-hl7 library.

Requirements:
    pip install hl7
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple
import hl7
import re

# Configuration
FOLDER_PATH = r"C:\\Users\\<USER>\\source\\repos\\Hl7EnhancePython\\enhancedHl7"  # Update this path
LOG_FILE = "hl7_validation_log_final.txt"

# Target HL7 version
TARGET_HL7_VERSION = '2.8'

# HL7 v2.8 Required segments for common message types
HL7_V28_MESSAGE_TYPES = {
    'ACK': ['MSH', 'MSA'],
    'ADT': ['MSH', 'EVN', 'PID'],
    'ORM': ['MSH', 'PID', 'ORC'],
    'ORU': ['MSH', 'PID', 'OBR'],
    'SIU': ['MSH', 'SCH'],
    'MDM': ['MSH', 'EVN', 'PID', 'TXA'],
    'DFT': ['MSH', 'EVN', 'PID', 'FT1'],
    'BAR': ['MSH', 'EVN', 'PID'],
    'VXU': ['MSH', 'PID', 'RXA']
}

# HL7 v2.8 Field validation rules
HL7_V28_MSH_FIELDS = {
    1: {'name': 'Field Separator', 'required': True, 'value': '|'},
    2: {'name': 'Encoding Characters', 'required': True, 'pattern': r'^\^~\\&'},
    3: {'name': 'Sending Application', 'required': False, 'max_length': 227},
    4: {'name': 'Sending Facility', 'required': False, 'max_length': 227},
    5: {'name': 'Receiving Application', 'required': False, 'max_length': 227},
    6: {'name': 'Receiving Facility', 'required': False, 'max_length': 227},
    7: {'name': 'Date/Time of Message', 'required': True, 'pattern': r'^\d{8,14}'},
    8: {'name': 'Security', 'required': False, 'max_length': 40},
    9: {'name': 'Message Type', 'required': True, 'pattern': r'^[A-Z\d]{3}\^[A-Z\d]{3}(\^[A-Z\d_]+)?'},
    10: {'name': 'Message Control ID', 'required': True, 'max_length': 199},
    11: {'name': 'Processing ID', 'required': True, 'pattern': r'^[DPTF]$'},
    12: {'name': 'Version ID', 'required': True, 'value': '2.8'}
}

# Common HL7 file extensions
HL7_EXTENSIONS = ['.hl7', '.txt', '.msg', '.dat', '.edi']


class HL7Validator:
    def __init__(self, folder_path: str):
        self.folder_path = folder_path
        self.total_processed = 0
        self.valid_count = 0
        self.invalid_count = 0
        self.invalid_details = []
        self.setup_logging()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, mode='w'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def is_hl7_file(self, file_path: str) -> bool:
        """Check if file is likely an HL7 file based on extension or content"""
        file_ext = Path(file_path).suffix.lower()
        if file_ext in HL7_EXTENSIONS:
            return True
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()
                return first_line.startswith('MSH')
        except Exception:
            return False

    def validate_hl7_version(self, parsed_msg) -> Tuple[bool, str]:
        """Validate HL7 version compliance - specifically for v2.8"""
        msh = parsed_msg.segment('MSH')
        if len(msh) < 12:
            return False, "MSH segment too short - version field missing"
        
        # FIX: Use index 11 for MSH-12
        version = str(msh[11]).strip()
        if version != TARGET_HL7_VERSION:
            return False, f"Invalid HL7 version: {version}. Expected: {TARGET_HL7_VERSION}"
        
        return True, f"Valid HL7 version: {version}"

    def validate_msh_fields_v28(self, parsed_msg) -> Tuple[bool, str]:
        """Validate MSH segment fields according to HL7 v2.8 standards"""
        msh = parsed_msg.segment('MSH')
        errors = []

        for field_num, rules in HL7_V28_MSH_FIELDS.items():
            # FIX: Use (field_num - 1) for 0-based index
            field_index = field_num - 1
            
            if field_index >= len(msh):
                if rules['required']:
                    errors.append(f"Required field MSH.{field_num} ({rules['name']}) is missing")
                continue

            field_value = str(msh[field_index]).strip()

            if rules['required'] and not field_value:
                errors.append(f"Required field MSH.{field_num} ({rules['name']}) is empty")
                continue

            if 'value' in rules and field_value and field_value != rules['value']:
                errors.append(f"MSH.{field_num} ({rules['name']}) has invalid value '{field_value}', expected '{rules['value']}'")

            if 'pattern' in rules and field_value and not re.match(rules['pattern'], field_value):
                errors.append(f"MSH.{field_num} ({rules['name']}) has invalid format: '{field_value}'")

            if 'max_length' in rules and len(field_value) > rules['max_length']:
                errors.append(f"MSH.{field_num} ({rules['name']}) exceeds max length of {rules['max_length']}")

        if errors:
            return False, "; ".join(errors)
        
        return True, "MSH segment fields are valid for HL7 v2.8"

    def validate_message_type_v28(self, parsed_msg) -> Tuple[bool, str]:
        """Validate message type and required segments for HL7 v2.8"""
        msh = parsed_msg.segment('MSH')
        if len(msh) < 9:
            return False, "Cannot determine message type - MSH.9 missing"

        # FIX: Use index 8 for MSH-9
        message_type_field = str(msh[8]).strip()
        if not message_type_field:
            return False, "Message type field (MSH.9) is empty"

        message_type = message_type_field.split('^')[0]
        if message_type not in HL7_V28_MESSAGE_TYPES:
            return True, f"Warning: Message type {message_type} not in configured list for segment validation."

        required_segments = HL7_V28_MESSAGE_TYPES[message_type]
        present_segments = [str(seg[0]) for seg in parsed_msg]
        missing_segments = [req for req in required_segments if req not in present_segments]

        if missing_segments:
            return False, f"Missing required segments for {message_type}: {', '.join(missing_segments)}"
        
        return True, f"Valid {message_type} message type with all required segments"

    def validate_file(self, file_path: str) -> Dict:
        """Validate a single HL7 file against v2.8 standards"""
        result = {'file_path': file_path, 'is_valid': True, 'errors': []}
        self.logger.info(f"Validating: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                result['errors'].append("File is empty")
                result['is_valid'] = False
                return result

            content = content.strip().replace('\n', '\r')
            
            parsed_msg = hl7.parse(content)
            if not parsed_msg or not parsed_msg.segment('MSH'):
                 result['errors'].append("HL7 parsing error: Missing MSH segment or invalid structure")
                 result['is_valid'] = False
                 return result

            validation_checks = [
                self.validate_hl7_version,
                self.validate_msh_fields_v28,
                self.validate_message_type_v28,
            ]
            
            for check_func in validation_checks:
                is_valid, message = check_func(parsed_msg)
                if not is_valid:
                    result['is_valid'] = False
                    result['errors'].append(f"{check_func.__name__}: {message}")

        except Exception as e:
            result['is_valid'] = False
            result['errors'].append(f"Critical file processing error: {str(e)}")
        
        return result

    def scan_folder(self) -> List[str]:
        """Scan folder and subfolders for HL7 files"""
        hl7_files = []
        if not os.path.exists(self.folder_path):
            self.logger.error(f"Folder path does not exist: {self.folder_path}")
            return hl7_files
        
        self.logger.info(f"Scanning folder: {self.folder_path}")
        for root, _, files in os.walk(self.folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_hl7_file(file_path):
                    hl7_files.append(file_path)
        
        self.logger.info(f"Found {len(hl7_files)} potential HL7 files")
        return hl7_files

    def process_files(self):
        """Process all HL7 files in the folder"""
        self.logger.info("Starting HL7 validation process...")
        start_time = datetime.now()
        
        hl7_files = self.scan_folder()
        if not hl7_files:
            self.logger.warning("No HL7 files found in the specified folder")
            return
        
        for file_path in hl7_files:
            self.total_processed += 1
            result = self.validate_file(file_path)
            
            if result['is_valid']:
                self.valid_count += 1
                self.logger.info(f"✓ VALID: {file_path}")
            else:
                self.invalid_count += 1
                self.logger.error(f"✗ INVALID: {file_path}")
                for error in result['errors']:
                    self.logger.error(f"  Error: {error}")
                self.invalid_details.append({'file': file_path, 'errors': result['errors']})
        
        end_time = datetime.now()
        self.generate_summary_report(start_time, end_time)
    
    def generate_summary_report(self, start_time: datetime, end_time: datetime):
        """Generate detailed summary report"""
        duration = end_time - start_time
        
        self.logger.info("\n" + "="*80)
        self.logger.info("HL7 v2.8 VALIDATION SUMMARY REPORT")
        self.logger.info("="*80)
        self.logger.info(f"Folder Path: {self.folder_path}")
        self.logger.info(f"Start Time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"Duration: {duration}")
        self.logger.info("-"*80)
        self.logger.info(f"Total Messages Processed: {self.total_processed}")
        self.logger.info(f"Valid Messages: {self.valid_count}")
        self.logger.info(f"Invalid Messages: {self.invalid_count}")
        
        if self.total_processed > 0:
            success_rate = (self.valid_count / self.total_processed) * 100
            self.logger.info(f"Success Rate: {success_rate:.2f}%")
        
        if self.invalid_details:
            self.logger.info("\n" + "-"*80)
            self.logger.info("DETAILED INVALID MESSAGE REPORT")
            self.logger.info("-"*80)
            for i, detail in enumerate(self.invalid_details, 1):
                self.logger.info(f"{i}. File: {detail['file']}")
                for error in detail['errors']:
                    self.logger.info(f"   • {error}")
                self.logger.info("")
        
        self.logger.info("="*80)
        self.logger.info(f"Report saved to: {LOG_FILE}")

def main():
    """Main function to run the HL7 v2.8 validator"""
    print(f"HL7 v2.8 Message Validator")
    print(f"Target Version: {TARGET_HL7_VERSION}")
    print(f"Folder Path: {FOLDER_PATH}")
    print("-" * 50)
    
    validator = HL7Validator(FOLDER_PATH)
    validator.process_files()

if __name__ == "__main__":
    main()