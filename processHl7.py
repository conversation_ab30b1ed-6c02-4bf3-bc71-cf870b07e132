import os
import sys
import json
import logging
import argparse
import traceback
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import time

try:
    import hl7
    import requests
    from dotenv import load_dotenv
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
    sys.exit(1)

# Load environment variables
load_dotenv()

class HL7ProcessingError(Exception):
    """Custom exception for HL7 processing errors"""
    def __init__(self, message: str, error_code: str, file_path: str = None):
        self.message = message
        self.error_code = error_code
        self.file_path = file_path
        super().__init__(self.message)

class HL7Processor:
    """Main HL7 processing class"""
    
    def __init__(self, source_dir: str, output_dir: str, verbose: bool = False):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.quarantine_dir = Path("quarantine")
        self.verbose = verbose
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_enhanced': 0,
            'errors_encountered': 0,
            'files_quarantined': 0
        }
        
        # Setup logging
        self.setup_logging()
        
        # Load configuration
        self.load_config()
        
        # Create directories
        self.create_directories()
    
    def setup_logging(self):
        """Setup dual logging system"""
        # Activity log (human-readable)
        activity_handler = logging.FileHandler('processing_activity.log')
        activity_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        activity_handler.setFormatter(activity_formatter)
        
        # Setup main logger
        self.logger = logging.getLogger('hl7_processor')
        self.logger.setLevel(logging.DEBUG if self.verbose else logging.INFO)
        self.logger.addHandler(activity_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # Error log (machine-readable JSON)
        self.error_log_path = Path('error_details.log')
    
    def log_error(self, error: HL7ProcessingError, context: Dict[str, Any] = None):
        """Log error in machine-readable JSON format"""
        error_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': 'error',
            'message': error.message,
            'filePath': str(error.file_path) if error.file_path else None,
            'errorCode': error.error_code,
            'errorContext': context or {},
            'service': 'hl7-enhancer'
        }
        
        with open(self.error_log_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(error_entry) + '\n')
    
    def load_config(self):
        """Load mapping rules and configuration"""
        try:
            with open('mapping_rules.json', 'r') as f:
                self.mapping_rules = json.load(f)
            self.logger.info(f"Loaded {len(self.mapping_rules)} mapping rules")
        except FileNotFoundError:
            self.logger.warning("mapping_rules.json not found, using empty rules")
            self.mapping_rules = []
        except json.JSONDecodeError as e:
            raise HL7ProcessingError(
                f"Invalid JSON in mapping_rules.json: {e}",
                "CONFIG_ERROR"
            )
    

    
    def create_directories(self):
        """Create necessary directories"""
        for directory in [self.output_dir, self.quarantine_dir]:
            try:
                directory.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise HL7ProcessingError(
                    f"Failed to create directory {directory}: {e}",
                    "DIRECTORY_ERROR"
                )
    
    def find_hl7_files(self) -> List[Path]:
        """Recursively find all HL7 files"""
        hl7_files = []
        try:
            for file_path in self.source_dir.rglob('*.hl7'):
                if file_path.is_file():
                    hl7_files.append(file_path)
            return hl7_files
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to scan directory {self.source_dir}: {e}",
                "DIRECTORY_ERROR"
            )
    
    def parse_hl7_message(self, file_path: Path) -> hl7.Message:
        """Parse HL7 file into message object"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            if not content:
                raise HL7ProcessingError(
                    "Empty file",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )
            
            # Preprocess HL7 content: ensure proper segment separators
            # HL7 library expects \r as segment separator, not \n
            import re
            if '\n' in content and '\r' not in content:
                # Convert newlines to carriage returns for HL7 library
                content = content.replace('\n', '\r')
            elif '\n' not in content and '\r' not in content:
                # Split by segment identifiers and rejoin with carriage returns
                content = re.sub(r'(?=MSH|EVN|PID|PV1|OBX|OBR|NTE|AL1|DG1|PR1|GT1|IN1|IN2|IN3|ACC|UB1|UB2|ZQA|ZFM)', r'\r', content)
                content = content.strip()
            
            # Parse HL7 message (hl7 library expects \r as segment separator)
            message = hl7.parse(content)
            
            # Validate MSH segment exists
            if not message.segment('MSH'):
                raise HL7ProcessingError(
                    "MSH segment not found",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )
            
            return message
            
        except hl7.ParseException as e:
            raise HL7ProcessingError(
                f"HL7 parsing failed: {e}",
                "HL7_PARSING_FAILURE",
                str(file_path)
            )
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to read file: {e}",
                "FILE_READ_ERROR",
                str(file_path)
            )
    
    def apply_hl7_enhancements(self, message: hl7.Message) -> hl7.Message:
        """Apply standard HL7 enhancements"""
        try:
            # Get MSH segment
            msh = message.segment('MSH')
            
            # Update version to 2.8
            msh[12] = '2.8'
            
            # Set processing ID to Production
            msh[11] = 'P'
            
            # Apply OBX mapping rules
            self.apply_obx_mapping(message)
            
            return message
            
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to apply enhancements: {e}",
                "PROCESSING_ERROR"
            )
    
    def apply_obx_mapping(self, message: hl7.Message):
        """Apply OBX segment mapping rules"""
        try:
            obx_segments = []
            segments_to_remove = []
            
            # Collect all OBX segments
            for i, segment in enumerate(message):
                segment_name = str(segment[0][0]) if len(segment) > 0 and len(segment[0]) > 0 else ''
                if segment_name == 'OBX':
                    obx_segments.append((i, segment))
            
            # Apply mapping rules
            for rule in self.mapping_rules:
                for seg_index, obx_segment in obx_segments:
                    # Check if this OBX matches the rule
                    # Extract OBX-3.1 (first component of observation identifier)
                    obx_field_value = ''
                    if len(obx_segment) > 3:
                        obx3_components = str(obx_segment[3]).split('^')
                        obx_field_value = obx3_components[0] if len(obx3_components) > 0 else ''
                    
                    if 'obxValue' in rule and obx_field_value == rule['obxValue']:
                        # Get the value to map (OBX-5)
                        value_to_map = str(obx_segment[5]) if len(obx_segment) > 5 else ''
                        
                        if value_to_map:
                            # Find or create target segment
                            try:
                                target_seg = message.segment(rule['targetSegment'])
                            except KeyError:
                                target_seg = None
                            
                            # If segment doesn't exist, create it
                            if not target_seg:
                                if rule['targetSegment'] == 'ROL':
                                    # Create ROL segment for family physician using message separators
                                    new_rol = hl7.Segment('ROL', ['', '', '', '', ''], separator=message.separator)
                                    # Find position after PID segment
                                    pid_index = -1
                                    for idx, seg in enumerate(message):
                                        if str(seg[0][0]) == 'PID':
                                            pid_index = idx
                                            break
                                    if pid_index >= 0:
                                        message.insert(pid_index + 1, new_rol)
                                        target_seg = new_rol
                                elif rule['targetSegment'] == 'PD1':
                                    # Create PD1 segment for primary facility using message separators
                                    new_pd1 = hl7.Segment('PD1', ['', '', '', ''], separator=message.separator)
                                    # Find position after PID segment
                                    pid_index = -1
                                    for idx, seg in enumerate(message):
                                        if str(seg[0][0]) == 'PID':
                                            pid_index = idx
                                            break
                                    if pid_index >= 0:
                                        message.insert(pid_index + 1, new_pd1)
                                        target_seg = new_pd1
                            
                            if target_seg:
                                # Parse target field (e.g., "3.8")
                                field_parts = rule['targetField'].split('.')
                                field_num = int(field_parts[0])
                                
                                # Ensure the segment has enough fields
                                while len(target_seg) <= field_num:
                                    target_seg.append('')
                                
                                # Set the value
                                if len(field_parts) > 1:
                                    component_num = int(field_parts[1]) - 1
                                    if not hasattr(target_seg[field_num], '__iter__') or isinstance(target_seg[field_num], str):
                                        target_seg[field_num] = ['']
                                    while len(target_seg[field_num]) <= component_num:
                                        target_seg[field_num].append('')
                                    target_seg[field_num][component_num] = value_to_map
                                else:
                                    target_seg[field_num] = value_to_map
                                
                                # Mark for removal if specified
                                if rule.get('removeOriginal', False):
                                    segments_to_remove.append(seg_index)
                                
                                self.logger.debug(f"Mapped {rule['obxValue']} to {rule['targetSegment']}-{rule['targetField']}")
                            else:
                                self.logger.warning(f"Could not create or find target segment {rule['targetSegment']} for {rule['obxValue']}")
            
            # Remove OBX segments (in reverse order to maintain indices)
            for seg_index in sorted(set(segments_to_remove), reverse=True):
                del message[seg_index]
                
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to apply OBX mapping: {e}",
                "MAPPING_RULE_ERROR"
            )
    
    def save_enhanced_message(self, message: hl7.Message, original_path: Path):
        """Save enhanced message preserving directory structure"""
        try:
            # Calculate relative path from source directory
            relative_path = original_path.relative_to(self.source_dir)
            output_path = self.output_dir / relative_path
            
            # Create parent directories
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert message back to string
            enhanced_content = str(message)
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)
            
            self.logger.info(f"Enhanced message saved: {output_path}")
            
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to save enhanced message: {e}",
                "FILE_WRITE_ERROR",
                str(original_path)
            )
    
    def quarantine_file(self, file_path: Path, error: HL7ProcessingError):
        """Move problematic file to quarantine with error details"""
        try:
            # Calculate relative path
            relative_path = file_path.relative_to(self.source_dir)
            quarantine_path = self.quarantine_dir / relative_path
            
            # Create parent directories
            quarantine_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file to quarantine
            import shutil
            shutil.copy2(file_path, quarantine_path)
            
            # Create error details file
            error_file = quarantine_path.with_suffix('.error.json')
            error_details = {
                'original_path': str(file_path),
                'error_code': error.error_code,
                'error_message': error.message,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            with open(error_file, 'w') as f:
                json.dump(error_details, f, indent=2)
            
            self.stats['files_quarantined'] += 1
            self.logger.warning(f"File quarantined: {quarantine_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to quarantine file {file_path}: {e}")
    

    
    def process_file(self, file_path: Path) -> bool:
        """Process a single HL7 file"""
        try:
            self.logger.info(f"Processing: {file_path}")
            
            # Parse HL7 message
            message = self.parse_hl7_message(file_path)
            
            # Apply enhancements
            enhanced_message = self.apply_hl7_enhancements(message)
            
            # Save enhanced message
            self.save_enhanced_message(enhanced_message, file_path)
            
            self.stats['files_enhanced'] += 1
            return True
            
        except HL7ProcessingError as e:
            self.stats['errors_encountered'] += 1
            
            # Log error
            self.log_error(e, {'stack': traceback.format_exc()})
            self.logger.error(f"Error processing {file_path}: {e.message}")
            
            # Quarantine file for certain error types
            if e.error_code in ['HL7_PARSING_FAILURE', 'PROCESSING_ERROR']:
                self.quarantine_file(file_path, e)
            
            return False
        
        except Exception as e:
            # Unexpected error
            error = HL7ProcessingError(
                f"Unexpected error: {e}",
                "PROCESSING_ERROR",
                str(file_path)
            )
            self.stats['errors_encountered'] += 1
            self.log_error(error, {'stack': traceback.format_exc()})
            self.logger.error(f"Unexpected error processing {file_path}: {e}")
            return False
    
    def run(self):
        """Main processing loop"""
        start_time = time.time()
        
        self.logger.info("🚀 Starting HL7 Message Enhancement Engine")
        
        try:
            # Find all HL7 files
            hl7_files = self.find_hl7_files()
            
            if not hl7_files:
                self.logger.warning(f"No HL7 files found in {self.source_dir}")
                return
            
            self.logger.info(f"Found {len(hl7_files)} HL7 files to process")
            
            # Process each file
            for file_path in hl7_files:
                self.stats['files_processed'] += 1
                self.process_file(file_path)
            
            # Final statistics
            elapsed_time = time.time() - start_time
            
            self.logger.info("\n✅ PROCESSING COMPLETE")
            self.logger.info(f"Files processed successfully: {self.stats['files_processed']}")
            self.logger.info(f"Files enhanced: {self.stats['files_enhanced']}")
            self.logger.info(f"Errors encountered: {self.stats['errors_encountered']}")
            self.logger.info(f"Files quarantined: {self.stats['files_quarantined']}")
            self.logger.info(f"Processing time: {elapsed_time:.2f} seconds")
            
            if self.stats['errors_encountered'] > 0:
                self.logger.info(f"Check error_details.log for detailed error analysis")
            
        except HL7ProcessingError as e:
            self.logger.error(f"Processing failed: {e.message}")
            self.log_error(e)
            sys.exit(1)
        except Exception as e:
            error = HL7ProcessingError(
                f"Unexpected system error: {e}",
                "PROCESSING_ERROR"
            )
            self.logger.error(f"System error: {e}")
            self.log_error(error, {'stack': traceback.format_exc()})
            sys.exit(1)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='HL7 Message Enhancement Engine'
    )
    parser.add_argument(
        '--source', 
        default='rawhl7messages',
        help='Source directory for raw HL7 files (default: rawhl7messages)'
    )
    parser.add_argument(
        '--output', 
        default='enhancedHl7',
        help='Output directory for enhanced files (default: enhancedHl7)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Validate source directory
    if not Path(args.source).exists():
        print(f"❌ Source directory not found: {args.source}")
        sys.exit(1)
    
    # Create and run processor
    try:
        processor = HL7Processor(args.source, args.output, args.verbose)
        processor.run()
    except KeyboardInterrupt:
        print("\n⚠️  Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to initialize processor: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()